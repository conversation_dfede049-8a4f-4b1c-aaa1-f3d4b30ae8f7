<template>
  <q-dialog v-model="dialogVisible" persistent>
    <q-card style="width: 600px; max-width: 90vw">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">新建工单</div>
        <q-space />
        <q-btn icon="close" flat round dense @click="closeDialog" />
      </q-card-section>

      <q-separator class="q-mt-md" />

      <q-card-section class="q-pt-md">
        <q-form @submit="submitTicket" ref="ticketForm">
          <div class="row q-col-gutter-md">
            <!-- 工单类型和优先级放在同一行 -->
            <div class="col-12 col-sm-8">
              <q-select
                v-model="formData.type"
                :options="ticketTypeOptions.filter(opt => opt.value > 0)"
                label="工单类型 *"
                outlined
                dense
                emit-value
                map-options
                :rules="[(val) => !!val || '请选择工单类型']" />
            </div>

            <div class="col-12 col-sm-4">
              <q-select
                v-model="formData.priority"
                :options="[
                  { label: '高', value: TicketApi.PRIORITY.HIGH },
                  { label: '中', value: TicketApi.PRIORITY.MEDIUM },
                  { label: '低', value: TicketApi.PRIORITY.LOW }
                ]"
                label="优先级"
                outlined
                dense
                emit-value
                map-options />
            </div>

            <div class="col-12">
              <q-input
                v-model="formData.title"
                label="工单标题 *"
                outlined
                dense
                :rules="[(val) => (!!val && val.length <= 200) || '请输入工单标题，不超过200字符']" />
            </div>

            <div class="col-12">
              <q-input
                v-model="formData.description"
                label="问题描述 *"
                type="textarea"
                outlined
                rows="6"
                :rules="[(val) => (!!val && val.length <= 1000) || '请输入问题描述，不超过1000字符']" />
              <div class="text-caption text-right text-grey-7">{{ formData.description.length }}/1000</div>
            </div>

            <!-- 图片上传区域 -->
            <div class="col-12">
              <div class="text-body2 text-weight-medium q-mb-sm">上传图片</div>
              <ImageUpload
                v-model="formData.attachmentUrls"
                :max-files="5"
                :max-file-size="10 * 1024 * 1024"
                accept="image/*"
                :multiple="true"
                :show-drop-zone="true"
              />
            </div>
          </div>

          <div class="row justify-end q-mt-md">
            <q-btn label="取消" flat color="primary" @click="closeDialog" />
            <q-btn 
              label="提交" 
              type="submit" 
              color="primary" 
              :loading="submitting"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { useQuasar } from 'quasar';
import TicketApi from '~/composables/ticketApi';
import ImageUpload from '~/components/ImageUpload.vue';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  defaultType: {
    type: Number,
    default: TicketApi.TICKET_TYPES.OTHER
  },
  defaultTitle: {
    type: String,
    default: ''
  },
  defaultDescription: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'success']);

const $q = useQuasar();

// 响应式数据
const submitting = ref(false);
const ticketForm = ref(null);

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 表单数据
const formData = reactive({
  type: TicketApi.TICKET_TYPES.OTHER,
  priority: TicketApi.PRIORITY.MEDIUM,
  title: '',
  description: '',
  attachmentUrls: [] // 存储上传的图片URL
});

const uploadedFiles = ref([]);



// 工单类型选项
const ticketTypeOptions = computed(() => {
  return [
    { label: '订单问题', value: TicketApi.TICKET_TYPES.ORDER },
    { label: '包裹问题', value: TicketApi.TICKET_TYPES.PACKAGE },
    { label: '商品问题', value: TicketApi.TICKET_TYPES.PRODUCT },
    { label: '售后服务', value: TicketApi.TICKET_TYPES.AFTER_SALE },
    { label: '投诉建议', value: TicketApi.TICKET_TYPES.COMPLAINT },
    { label: '其他问题', value: TicketApi.TICKET_TYPES.OTHER }
  ];
});

// 监听props变化，初始化表单数据
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    formData.type = props.defaultType;
    formData.title = props.defaultTitle;
    formData.description = props.defaultDescription;
    formData.priority = TicketApi.PRIORITY.MEDIUM;
    formData.attachmentUrls = []; // 重置附件URLs
    uploadedFiles.value = [];
  }
});

// 监听附件URLs变化
watch(() => formData.attachmentUrls, (newVal, oldVal) => {
  // 只有当值真正发生变化时才打印日志
  if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    console.log('TicketDialog - attachmentUrls变化:', newVal);
  }
}, { deep: true });

// 方法
const closeDialog = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  formData.type = TicketApi.TICKET_TYPES.OTHER;
  formData.priority = TicketApi.PRIORITY.MEDIUM;
  formData.title = '';
  formData.description = '';
  // 使用nextTick确保在下一个tick中重置，避免递归更新
  nextTick(() => {
    formData.attachmentUrls = [];
    console.log('表单已重置，attachmentUrls:', formData.attachmentUrls);
  });
};

// 提交工单
const submitTicket = async () => {
  try {
    submitting.value = true;

    const response = await TicketApi.create({
      ...formData
    });
    
    if (response.code === 0) {
      $q.notify({
        color: 'positive',
        message: '工单创建成功',
        icon: 'check',
      });
      
      emit('success', response.data);
      closeDialog();
    } else {
      $q.notify({
        color: 'negative',
        message: response.msg || '工单创建失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('工单创建失败', error);
    $q.notify({
      color: 'negative',
      message: '工单创建失败',
      icon: 'error',
    });
  } finally {
    submitting.value = false;
  }
};
</script>

<style lang="scss" scoped>
// 移动端适配
@media (max-width: 599px) {
  :deep(.q-card) {
    margin: 16px;
  }
}
</style>
