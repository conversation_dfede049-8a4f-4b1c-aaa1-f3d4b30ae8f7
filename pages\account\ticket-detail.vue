<template>
  <div class="ticket-detail">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-btn flat round dense icon="arrow_back" @click="goBack" class="q-mr-sm" />
        <q-icon name="assignment" size="xs" color="orange-6" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">工单详情</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="q-pa-lg text-center">
      <q-spinner-dots size="40px" color="primary" />
      <div class="q-mt-sm text-grey-7">加载中...</div>
    </div>

    <!-- 工单详情内容 -->
    <div v-else-if="ticket" class="q-pa-md">
      <!-- 工单基本信息 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="row items-center q-mb-md">
            <div class="text-h6 text-weight-medium">{{ ticket.title }}</div>
            <q-space />
            <q-badge :color="getStatusColor(ticket.status)" class="q-px-sm q-py-xs">
              {{ getStatusName(ticket.status) }}
            </q-badge>
          </div>

          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6">
              <div class="info-item">
                <span class="info-label">工单号：</span>
                <span class="info-value">{{ ticket.no }}</span>
              </div>
            </div>
            <div class="col-12 col-sm-6">
              <div class="info-item">
                <span class="info-label">工单类型：</span>
                <q-badge :color="getTypeColor(ticket.type)" text-color="white">
                  {{ getTypeName(ticket.type) }}
                </q-badge>
              </div>
            </div>
            <div class="col-12 col-sm-6">
              <div class="info-item">
                <span class="info-label">优先级：</span>
                <q-badge :color="ticket.priority === 1 ? 'red' : ticket.priority === 2 ? 'orange' : 'green'">
                  {{ getPriorityName(ticket.priority) }}
                </q-badge>
              </div>
            </div>
            <div class="col-12 col-sm-6">
              <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span class="info-value">{{ formatDateTime(ticket.createTime) }}</span>
              </div>
            </div>
            <div class="col-12 col-sm-6" v-if="ticket.adminName">
              <div class="info-item">
                <span class="info-label">处理客服：</span>
                <span class="info-value">{{ ticket.adminName }}</span>
              </div>
            </div>
            <div class="col-12 col-sm-6">
              <div class="info-item">
                <span class="info-label">更新时间：</span>
                <span class="info-value">{{ formatDateTime(ticket.updateTime) }}</span>
              </div>
            </div>
          </div>

          <div class="q-mt-md">
            <div class="info-label">问题描述：</div>
            <div class="info-value q-mt-xs">{{ ticket.description }}</div>
          </div>

          <!-- 初始附件 -->
          <div v-if="ticket.attachmentUrls && ticket.attachmentUrls.length > 0" class="q-mt-md">
            <div class="info-label">相关附件：</div>
            <div class="row q-gutter-sm q-mt-xs">
              <div v-for="(url, index) in ticket.attachmentUrls" :key="index" class="attachment-item">
                <q-img
                  v-if="isImage(url)"
                  :src="url"
                  class="attachment-image cursor-pointer"
                  @click="previewImage(url)"
                  fit="cover"
                />
                <q-btn
                  v-else
                  outline
                  color="primary"
                  icon="attach_file"
                  :label="getFileName(url)"
                  @click="downloadFile(url)"
                  class="attachment-file"
                />
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- 处理记录时间线 -->
      <q-card flat bordered class="q-mb-md">
        <q-card-section>
          <div class="text-h6 text-weight-medium q-mb-md">处理记录</div>

          <q-timeline v-if="ticket.messages && ticket.messages.length > 0" color="primary">
            <q-timeline-entry
              v-for="message in ticket.messages"
              :key="message.id"
              :color="message.messageType === 1 ? 'blue' : 'green'"
              :icon="message.messageType === 1 ? 'person' : 'support_agent'"
            >
              <template v-slot:title>
                <div class="row items-center">
                  <span class="text-weight-medium">{{ message.replierName }}</span>
                  <q-badge
                    :color="message.messageType === 1 ? 'blue' : 'green'"
                    text-color="white"
                    class="q-ml-sm"
                  >
                    {{ message.messageTypeName }}
                  </q-badge>
                </div>
              </template>

              <template v-slot:subtitle>
                {{ formatDateTime(message.createTime) }}
              </template>

              <div class="message-content">
                <div class="message-text">{{ message.content }}</div>

                <!-- 消息附件 -->
                <div v-if="message.attachmentUrls && message.attachmentUrls.length > 0" class="q-mt-sm">
                  <div class="row q-gutter-sm">
                    <div v-for="(url, index) in message.attachmentUrls" :key="index" class="attachment-item">
                      <q-img
                        v-if="isImage(url)"
                        :src="url"
                        class="attachment-image cursor-pointer"
                        @click="previewImage(url)"
                        fit="cover"
                      />
                      <q-btn
                        v-else
                        outline
                        color="primary"
                        icon="attach_file"
                        :label="getFileName(url)"
                        @click="downloadFile(url)"
                        class="attachment-file"
                        size="sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </q-timeline-entry>
          </q-timeline>

          <div v-else class="text-center q-py-lg text-grey-7">
            暂无处理记录
          </div>
        </q-card-section>
      </q-card>

      <!-- 回复区域 -->
      <q-card
        v-if="canReply"
        flat
        bordered
        class="q-mb-md"
      >
        <q-card-section>
          <div class="text-h6 text-weight-medium q-mb-md">添加回复</div>

          <q-form @submit="submitReply" ref="replyForm">
            <div class="q-mb-md">
              <q-input
                v-model="replyData.content"
                type="textarea"
                outlined
                autogrow
                label="回复内容 *"
                :rules="[(val) => (!!val && val.length <= 1000) || '请输入回复内容，不超过1000字符']"
                rows="4"
              />
              <div class="text-caption text-right text-grey-7">{{ replyData.content.length }}/1000</div>
            </div>

            <!-- 文件上传区域 -->
            <div class="q-mb-md">
              <div class="text-body2 text-weight-medium q-mb-sm">上传附件</div>

              <!-- 已上传的文件 -->
              <div v-if="uploadedFiles.length > 0" class="uploaded-files q-mb-sm">
                <div class="row q-gutter-sm">
                  <div v-for="(file, index) in uploadedFiles" :key="index" class="uploaded-file-item">
                    <q-img
                      v-if="file.isImage"
                      :src="file.url"
                      class="uploaded-image"
                      fit="cover"
                    />
                    <div v-else class="uploaded-file">
                      <q-icon name="attach_file" size="24px" color="primary" />
                      <div class="file-name">{{ file.name }}</div>
                    </div>
                    <q-btn
                      round
                      dense
                      flat
                      icon="close"
                      color="negative"
                      class="remove-btn"
                      @click="removeFile(index)"
                    />
                  </div>
                </div>
              </div>

              <!-- 上传按钮 -->
              <q-btn
                outline
                color="primary"
                icon="cloud_upload"
                label="选择文件"
                @click="triggerFileUpload"
                :disable="uploadedFiles.length >= 5"
              />

              <input
                ref="fileInput"
                type="file"
                multiple
                accept="image/*,.pdf,.doc,.docx,.txt"
                style="display: none"
                @change="onFileSelect"
              />

              <div class="text-caption text-grey-7 q-mt-xs">
                支持图片、PDF、Word文档，单个文件不超过10MB，最多5个文件
              </div>
            </div>

            <div class="row justify-end q-gutter-sm">
              <q-btn
                label="取消"
                flat
                color="primary"
                @click="cancelReply"
              />
              <q-btn
                label="提交回复"
                type="submit"
                color="primary"
                :loading="submitting"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>

      <!-- 操作按钮 -->
      <div class="row justify-end q-gutter-sm">
        <q-btn
          v-if="ticket.status === TicketApi.TICKET_STATUS.RESOLVED && !ticket.rating"
          outline
          color="orange"
          icon="star"
          label="评价"
          @click="openRatingDialog"
        />
        <q-btn
          v-if="canClose"
          outline
          color="negative"
          icon="close"
          label="关闭工单"
          @click="confirmCloseTicket"
        />
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="q-pa-lg text-center">
      <q-icon name="assignment" size="3em" color="grey-5" />
      <div class="q-mt-sm text-grey-7">工单不存在或已被删除</div>
    </div>

    <!-- 评价弹窗 -->
    <q-dialog v-model="ratingDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="row items-center">
          <div class="text-h6">工单评价</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section>
          <div class="q-mb-md">
            <div class="text-body2 q-mb-sm">服务评分 *</div>
            <q-rating
              v-model="ratingData.rating"
              size="2em"
              color="orange"
              icon="star"
            />
          </div>

          <div class="q-mb-md">
            <q-input
              v-model="ratingData.comment"
              type="textarea"
              outlined
              autogrow
              label="评价内容（可选）"
              rows="3"
              maxlength="500"
            />
            <div class="text-caption text-right text-grey-7">{{ ratingData.comment.length }}/500</div>
          </div>

          <div class="row justify-end q-gutter-sm">
            <q-btn label="取消" flat color="primary" v-close-popup />
            <q-btn
              label="提交评价"
              color="primary"
              @click="submitRating"
              :loading="ratingSubmitting"
            />
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 关闭确认弹窗 -->
    <q-dialog v-model="closeDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="warning" text-color="white" />
          <span class="q-ml-sm">确认关闭工单</span>
        </q-card-section>

        <q-card-section>
          关闭后的工单将无法继续回复，确定要关闭此工单吗？
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn
            flat
            label="确认关闭"
            color="negative"
            @click="closeTicket"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 图片预览弹窗 -->
    <q-dialog v-model="imagePreviewDialog">
      <q-card class="image-preview-card">
        <q-card-section class="q-pa-none">
          <q-img :src="previewImageUrl" fit="contain" class="preview-image" />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="关闭" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import TicketApi from '~/composables/ticketApi';

const route = useRoute();
const router = useRouter();
const $q = useQuasar();

// 响应式数据
const loading = ref(true);
const ticket = ref(null);
const submitting = ref(false);
const ratingSubmitting = ref(false);

// 弹窗状态
const ratingDialog = ref(false);
const closeDialog = ref(false);
const imagePreviewDialog = ref(false);
const previewImageUrl = ref('');

// 回复数据
const replyForm = ref(null);
const replyData = reactive({
  content: ''
});

// 文件上传
const fileInput = ref(null);
const uploadedFiles = ref([]);

// 评价数据
const ratingData = reactive({
  rating: 5,
  comment: ''
});

// 计算属性
const canReply = computed(() => {
  return ticket.value &&
         ticket.value.status !== TicketApi.TICKET_STATUS.CLOSED &&
         ticket.value.status !== TicketApi.TICKET_STATUS.CANCELLED;
});

const canClose = computed(() => {
  return ticket.value &&
         ticket.value.status !== TicketApi.TICKET_STATUS.CLOSED &&
         ticket.value.status !== TicketApi.TICKET_STATUS.CANCELLED;
});

// 方法
const goBack = () => {
  router.back();
};

const loadTicketDetail = async () => {
  try {
    loading.value = true;
    const ticketId = route.query.id;
    const response = await TicketApi.getDetail(ticketId);

    if (response.code === 0) {
      ticket.value = response.data;
    } else {
      $q.notify({
        color: 'negative',
        message: response.msg || '获取工单详情失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('获取工单详情失败', error);
    $q.notify({
      color: 'negative',
      message: '获取工单详情失败',
      icon: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '-';
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 获取状态相关方法
const getStatusColor = (status) => {
  return TicketApi.getStatusColor(status);
};

const getStatusName = (status) => {
  return TicketApi.getStatusName(status);
};

const getTypeColor = (type) => {
  return TicketApi.getTypeColor(type);
};

const getTypeName = (type) => {
  return TicketApi.getTypeName(type);
};

const getPriorityName = (priority) => {
  return TicketApi.getPriorityName(priority);
};

// 文件处理方法
const isImage = (url) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  return imageExtensions.some(ext => url.toLowerCase().includes(ext));
};

const getFileName = (url) => {
  return url.split('/').pop() || '附件';
};

const previewImage = (url) => {
  previewImageUrl.value = url;
  imagePreviewDialog.value = true;
};

const downloadFile = (url) => {
  window.open(url, '_blank');
};

// 文件上传相关方法
const triggerFileUpload = () => {
  fileInput.value?.click();
};

const onFileSelect = async (event) => {
  const files = Array.from(event.target.files);

  for (const file of files) {
    if (uploadedFiles.value.length >= 5) {
      $q.notify({
        color: 'warning',
        message: '最多只能上传5个文件',
        icon: 'warning',
      });
      break;
    }

    if (file.size > 10 * 1024 * 1024) {
      $q.notify({
        color: 'negative',
        message: `文件 ${file.name} 超过10MB限制`,
        icon: 'error',
      });
      continue;
    }

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await TicketApi.uploadFile(formData);
      if (response.code === 0) {
        uploadedFiles.value.push({
          name: file.name,
          url: response.data,
          isImage: file.type.startsWith('image/')
        });
      }
    } catch (error) {
      console.error('文件上传失败', error);
      $q.notify({
        color: 'negative',
        message: `文件 ${file.name} 上传失败`,
        icon: 'error',
      });
    }
  }

  // 清空input
  event.target.value = '';
};

const removeFile = (index) => {
  uploadedFiles.value.splice(index, 1);
};

// 回复相关方法
const submitReply = async () => {
  try {
    submitting.value = true;

    const attachmentUrls = uploadedFiles.value.map(file => file.url);

    const response = await TicketApi.reply(ticket.value.id, {
      content: replyData.content,
      attachmentUrls
    });

    if (response.code === 0) {
      $q.notify({
        color: 'positive',
        message: '回复提交成功',
        icon: 'check',
      });

      // 重置表单
      replyData.content = '';
      uploadedFiles.value = [];

      // 重新加载工单详情
      await loadTicketDetail();
    } else {
      $q.notify({
        color: 'negative',
        message: response.msg || '回复提交失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('回复提交失败', error);
    $q.notify({
      color: 'negative',
      message: '回复提交失败',
      icon: 'error',
    });
  } finally {
    submitting.value = false;
  }
};

const cancelReply = () => {
  replyData.content = '';
  uploadedFiles.value = [];
};

// 评价相关方法
const openRatingDialog = () => {
  ratingDialog.value = true;
};

const submitRating = async () => {
  try {
    ratingSubmitting.value = true;

    const response = await TicketApi.rate(ticket.value.id, {
      rating: ratingData.rating,
      comment: ratingData.comment
    });

    if (response.code === 0) {
      $q.notify({
        color: 'positive',
        message: '评价提交成功',
        icon: 'check',
      });

      ratingDialog.value = false;
      await loadTicketDetail();
    } else {
      $q.notify({
        color: 'negative',
        message: response.msg || '评价提交失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('评价提交失败', error);
    $q.notify({
      color: 'negative',
      message: '评价提交失败',
      icon: 'error',
    });
  } finally {
    ratingSubmitting.value = false;
  }
};

// 关闭工单相关方法
const confirmCloseTicket = () => {
  closeDialog.value = true;
};

const closeTicket = async () => {
  try {
    const response = await TicketApi.close(ticket.value.id);

    if (response.code === 0) {
      $q.notify({
        color: 'positive',
        message: '工单关闭成功',
        icon: 'check',
      });

      await loadTicketDetail();
    } else {
      $q.notify({
        color: 'negative',
        message: response.msg || '工单关闭失败',
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('工单关闭失败', error);
    $q.notify({
      color: 'negative',
      message: '工单关闭失败',
      icon: 'error',
    });
  }
};

// 页面加载时获取工单详情
onMounted(() => {
  loadTicketDetail();
});
</script>

<style lang="scss" scoped>
.ticket-detail {
  min-height: 100vh;
  background-color: #f5f5f5;

  .info-item {
    margin-bottom: 8px;

    .info-label {
      color: #666;
      font-weight: 500;
      margin-right: 8px;
    }

    .info-value {
      color: #333;
    }
  }

  .message-content {
    .message-text {
      line-height: 1.6;
      color: #333;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .attachment-item {
    position: relative;

    .attachment-image {
      width: 80px;
      height: 80px;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
    }

    .attachment-file {
      max-width: 200px;
    }
  }

  .uploaded-files {
    .uploaded-file-item {
      position: relative;
      display: inline-block;
      margin-right: 8px;
      margin-bottom: 8px;

      .uploaded-image {
        width: 80px;
        height: 80px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
      }

      .uploaded-file {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background-color: #f9f9f9;
        max-width: 200px;

        .file-name {
          margin-left: 8px;
          font-size: 12px;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .remove-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        background-color: white;
        border: 1px solid #e0e0e0;
        width: 20px;
        height: 20px;
        min-height: 20px;
      }
    }
  }

  .image-preview-card {
    max-width: 90vw;
    max-height: 90vh;

    .preview-image {
      max-height: 80vh;
    }
  }

  // 移动端适配
  @media (max-width: 599px) {
    .q-pa-md {
      padding: 8px;
    }

    .info-item {
      .info-label {
        display: block;
        margin-bottom: 4px;
      }
    }

    .attachment-image,
    .uploaded-image {
      width: 60px !important;
      height: 60px !important;
    }

    .uploaded-file {
      max-width: 150px !important;
    }
  }
}
</style>