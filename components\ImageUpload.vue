<template>
  <div class="image-upload-container">
    <!-- 已上传的图片列表 -->
    <div v-if="imageList.length > 0" class="uploaded-images q-mb-md">
      <div class="row q-col-gutter-sm">
        <div
          v-for="(image, index) in imageList"
          :key="index"
          class="col-6 col-sm-4 col-md-3"
        >
          <div class="image-item">
            <q-img
              :src="image.url"
              :ratio="1"
              class="uploaded-image"
              @click="previewImage(image.url)"
              fit="cover"
            >
              <template v-slot:loading>
                <div class="absolute-full flex flex-center">
                  <q-spinner-gears color="primary" size="2em" />
                </div>
              </template>

              <!-- 上传进度覆盖层 -->
              <div v-if="image.uploading" class="absolute-full bg-black-50 flex flex-center column">
                <q-circular-progress
                  :value="image.progress"
                  size="50px"
                  :thickness="0.2"
                  color="white"
                  track-color="grey-3"
                  class="q-ma-md"
                />
                <div class="text-white text-caption">{{ image.progress }}%</div>
              </div>
            </q-img>

            <!-- 删除按钮 -->
            <q-btn
              v-if="!disabled"
              icon="close"
              size="xs"
              round
              color="negative"
              class="delete-btn"
              @click="removeImage(index)"
            />
          </div>
        </div>
      </div>
    </div>



    <!-- 拖拽上传区域 -->
    <div
      v-if="!disabled && imageList.length < maxFiles"
      class="drop-zone"
      :class="{ 'drop-zone-active': isDragOver }"
      @drop="onDrop"
      @dragover="onDragOver"
      @dragleave="onDragLeave"
      @click="triggerFileInput"
    >
      <div class="drop-zone-content">
        <q-icon name="cloud_upload" size="3em" color="primary" />
        <div class="q-mt-sm text-grey-8 text-weight-medium">
          点击或拖拽图片到此处上传
        </div>
        <div class="text-caption text-grey-6 q-mt-xs">
          支持 {{ acceptText }}，单个文件不超过 {{ maxFileSizeText }}
        </div>
        <div class="text-caption text-grey-6">
          还可以上传 {{ maxFiles - imageList.length }} 张图片
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :multiple="multiple"
      style="display: none"
      @change="onFileInputChange"
    />

    <!-- 图片预览对话框 -->
    <q-dialog v-model="previewDialog">
      <q-card>
        <q-card-section class="q-pa-none">
          <q-img :src="previewImageUrl" style="max-width: 90vw; max-height: 90vh;" />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="关闭" color="primary" @click="previewDialog = false" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { uploadFile as uploadFileApi } from '~/composables/fileApi';

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxFiles: {
    type: Number,
    default: 5
  },
  maxFileSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  multiple: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showDropZone: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// Quasar
const $q = useQuasar();

// Refs
const fileInputRef = ref(null);
const imageList = ref([]);
const isDragOver = ref(false);
const previewDialog = ref(false);
const previewImageUrl = ref('');

// Computed
const acceptText = computed(() => {
  return props.accept.includes('image') ? 'JPG、PNG、GIF等图片格式' : '文件';
});

const maxFileSizeText = computed(() => {
  const size = props.maxFileSize / (1024 * 1024);
  return `${size}MB`;
});

// Watch
watch(() => props.modelValue, (newVal) => {
  if (newVal && Array.isArray(newVal)) {
    imageList.value = newVal.map(url => ({
      url,
      uploading: false,
      progress: 100
    }));
  }
}, { immediate: true });

watch(imageList, (newVal) => {
  const urls = newVal.filter(item => !item.uploading).map(item => item.url);
  console.log('imageList变化:', newVal); // 添加调试日志
  console.log('提取的URLs:', urls); // 添加调试日志
  emit('update:modelValue', urls);
}, { deep: true });

// Methods

const validateFile = (file) => {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    $q.notify({
      color: 'negative',
      message: '只能上传图片文件',
      icon: 'error'
    });
    return false;
  }

  // 检查文件大小
  if (file.size > props.maxFileSize) {
    $q.notify({
      color: 'negative',
      message: `文件大小不能超过 ${maxFileSizeText.value}`,
      icon: 'error'
    });
    return false;
  }

  return true;
};

const uploadFile = async (file) => {
  const imageItem = {
    url: '',
    uploading: true,
    progress: 0,
    file
  };

  imageList.value.push(imageItem);

  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (imageItem.progress < 90) {
        imageItem.progress += 10;
      }
    }, 100);

    // 调用上传API（不传递path参数，让后端随机命名）
    const response = await uploadFileApi(file);

    clearInterval(progressInterval);

    console.log('上传响应:', response); // 添加调试日志

    if (response.code === 0) {
      imageItem.url = response.data;
      imageItem.uploading = false;
      imageItem.progress = 100;

      console.log('上传成功，图片URL:', response.data); // 添加调试日志
      console.log('当前imageList:', imageList.value); // 添加调试日志

      $q.notify({
        color: 'positive',
        message: '图片上传成功',
        icon: 'check'
      });
    } else {
      throw new Error(response.msg || '上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);

    // 移除失败的项目
    const index = imageList.value.indexOf(imageItem);
    if (index > -1) {
      imageList.value.splice(index, 1);
    }

    $q.notify({
      color: 'negative',
      message: error.message || '图片上传失败',
      icon: 'error'
    });
  }
};

const removeImage = (index) => {
  imageList.value.splice(index, 1);
};

const previewImage = (url) => {
  previewImageUrl.value = url;
  previewDialog.value = true;
};

const triggerFileInput = () => {
  fileInputRef.value?.click();
};

const onFileInputChange = (event) => {
  const files = Array.from(event.target.files || []);
  files.forEach(file => {
    if (validateFile(file)) {
      uploadFile(file);
    }
  });
  event.target.value = '';
};

// 拖拽相关方法
const onDragOver = (event) => {
  event.preventDefault();
  isDragOver.value = true;
};

const onDragLeave = () => {
  isDragOver.value = false;
};

const onDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;
  
  const files = Array.from(event.dataTransfer.files || []);
  files.forEach(file => {
    if (validateFile(file)) {
      uploadFile(file);
    }
  });
};


</script>

<style scoped>
.image-upload-container {
  width: 100%;
}

.uploaded-images {
  max-height: 300px;
  overflow-y: auto;
}

.image-item {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
}

.uploaded-image:hover {
  border-color: #1976d2;
  transform: scale(1.02);
}

.delete-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 2;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.drop-zone {
  border: 2px dashed #e0e0e0;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone:hover,
.drop-zone-active {
  border-color: #1976d2;
  background-color: #f0f8ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.1);
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

/* 移动端适配 */
@media (max-width: 599px) {
  .uploaded-images {
    max-height: 200px;
  }

  .drop-zone {
    padding: 30px 15px;
    min-height: 100px;
  }

  .drop-zone-content {
    gap: 6px;
  }

  .drop-zone-content .q-icon {
    font-size: 2em !important;
  }
}
</style>
