<template>
  <div class="message-consult">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="question_answer" size="xs" color="orange-6" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('messageCenter.consult.pageTitle') }}</span>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section q-pa-md">
      <div class="row q-col-gutter-md items-center">
        <div class="col-12 col-sm-auto">
          <q-select v-model="filter.type" :options="ticketTypeOptions" label="工单类型" outlined dense emit-value map-options options-dense class="ticket-filter" @update:model-value="onFilterChange" />
        </div>
        <div class="col-12 col-sm-auto">
          <q-select v-model="filter.status" :options="ticketStatusOptions" label="状态筛选" outlined dense emit-value map-options options-dense class="ticket-filter" @update:model-value="onFilterChange" />
        </div>
        <div class="col-auto q-ml-auto">
          <q-btn color="primary" label="新建工单" icon="add" @click="openNewTicketDialog" />
        </div>
      </div>
    </div>

    <!-- 工单列表 -->
    <div class="ticket-list q-pa-md">
      <!-- PC端表格视图 -->
      <div v-if="!$q.screen.lt.sm" class="desktop-view">
        <q-table
          :rows="state.pagination.list"
          :columns="columns"
          row-key="id"
          :pagination="pagination"
          :loading="state.loadStatus"
          :rows-per-page-options="[10, 20, 50]"
          @request="onRequest"
          binary-state-sort
          flat
          bordered>
          <template v-slot:body="props">
            <q-tr :props="props" @click="openTicketDetail(props.row)" class="cursor-pointer">
              <q-td key="no" :props="props">
                {{ props.row.no }}
              </q-td>
              <q-td key="type" :props="props">
                <q-badge :color="getTicketTypeColor(props.row.type)" text-color="white">
                  {{ getTicketTypeName(props.row.type) }}
                </q-badge>
              </q-td>
              <q-td key="title" :props="props">
                {{ props.row.title }}
              </q-td>
              <q-td key="priority" :props="props">
                <q-badge :color="props.row.priority === 1 ? 'red' : props.row.priority === 2 ? 'orange' : 'green'">
                  {{ getPriorityName(props.row.priority) }}
                </q-badge>
              </q-td>
              <q-td key="status" :props="props">
                <q-badge :color="getStatusColor(props.row.status)">
                  {{ getStatusName(props.row.status) }}
                </q-badge>
              </q-td>
              <q-td key="createTime" :props="props">
                {{ formatDateTime(props.row.createTime) }}
              </q-td>
              <q-td key="updateTime" :props="props">
                {{ formatDateTime(props.row.updateTime) }}
              </q-td>
              <q-td key="actions" :props="props" auto-width>
                <q-btn flat round dense icon="visibility" color="primary" @click.stop="openTicketDetail(props.row)" />
                <q-btn v-if="props.row.status !== TicketApi.TICKET_STATUS.CLOSED" flat round dense icon="close" color="negative" @click.stop="confirmCloseTicket(props.row)" />
              </q-td>
            </q-tr>
          </template>

          <template v-slot:no-data>
            <div class="full-width row flex-center q-py-lg">
              <q-icon name="assignment" size="2em" color="grey-5" />
              <div class="q-ml-sm text-grey-7">暂无工单数据</div>
            </div>
          </template>

          <template v-slot:pagination="scope">
            <div class="row items-center justify-end q-py-sm">
              <div class="col-auto">
                <q-pagination
                  v-model="state.pagination.pageNo"
                  :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                  :max-pages="6"
                  :boundary-links="$q.screen.gt.xs"
                  :direction-links="true"
                  @update:model-value="onPageChange"
                />
              </div>
            </div>
          </template>
        </q-table>
      </div>

      <!-- 移动端卡片视图 -->
      <div v-else class="mobile-view">
        <div v-if="state.pagination.list.length === 0 && !state.loadStatus" class="text-center q-py-xl">
          <q-icon name="assignment" size="3em" color="grey-5" />
          <div class="q-mt-sm text-grey-7">暂无工单数据</div>
        </div>

        <q-list separator>
          <q-item v-for="ticket in state.pagination.list" :key="ticket.id" clickable v-ripple @click="openTicketDetail(ticket)">
            <q-item-section>
              <q-item-label class="row items-center">
                <q-badge :color="getTicketTypeColor(ticket.type)" text-color="white" class="q-mr-sm">
                  {{ getTicketTypeName(ticket.type) }}
                </q-badge>
                <span class="text-weight-medium">{{ ticket.title }}</span>
              </q-item-label>
              <q-item-label caption class="q-mt-xs">
                <div class="row justify-between">
                  <span>工单号: {{ ticket.no }}</span>
                  <q-badge :color="getStatusColor(ticket.status)">
                    {{ getStatusName(ticket.status) }}
                  </q-badge>
                </div>
              </q-item-label>
              <q-item-label caption class="q-mt-xs">
                <div class="row justify-between">
                  <span>创建时间: {{ formatDate(ticket.createTime) }}</span>
                  <!-- <q-badge :color="ticket.priority === 1 ? 'red' : ticket.priority === 2 ? 'orange' : 'green'">
                    {{ getPriorityName(ticket.priority) }}
                  </q-badge> -->
                </div>
              </q-item-label>
            </q-item-section>

            <q-item-section side>
              <div class="row">
                <q-btn flat round dense icon="visibility" color="primary" @click.stop="openTicketDetail(ticket)" />
                <q-btn v-if="ticket.status !== TicketApi.TICKET_STATUS.CLOSED" flat round dense icon="close" color="negative" @click.stop="confirmCloseTicket(ticket)" />
              </div>
            </q-item-section>
          </q-item>
        </q-list>

        <!-- 移动端分页 -->
        <div class="row justify-center q-py-md">
          <q-pagination
            v-model="state.pagination.pageNo"
            :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
            :max-pages="5"
            :boundary-links="false"
            :direction-links="true"
            @update:model-value="onPageChange"
          />
        </div>
      </div>
    </div>

    <!-- 新建工单弹窗 -->
    <TicketDialog
      v-model="newTicketDialog"
      @success="onTicketCreated"
    />

    <!-- 关闭工单确认弹窗 -->
    <q-dialog v-model="closeTicketDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="warning" text-color="white" />
          <span class="q-ml-sm">确认关闭工单</span>
        </q-card-section>

        <q-card-section>关闭后的工单将无法继续回复，确定要关闭此工单吗？</q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="确认关闭" color="negative" @click="closeTicket" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useQuasar, date } from 'quasar';
import { useRouter } from 'vue-router';
import TicketApi from '~/composables/ticketApi';
import TicketDialog from '~/components/TicketDialog.vue';

const $q = useQuasar();
const router = useRouter();

// 筛选条件
const filter = reactive({
  type: 0, // 0表示全部
  status: 0, // 0表示全部
});

// 工单类型选项
const ticketTypeOptions = [
  { label: '全部类型', value: 0 },
  { label: '订单问题', value: TicketApi.TICKET_TYPES.ORDER },
  { label: '商品问题', value: TicketApi.TICKET_TYPES.PRODUCT },
  { label: '包裹问题', value: TicketApi.TICKET_TYPES.PACKAGE },
  { label: '物流问题', value: TicketApi.TICKET_TYPES.LOGISTICS },
  { label: '账户问题', value: TicketApi.TICKET_TYPES.ACCOUNT },
  { label: '其他问题', value: TicketApi.TICKET_TYPES.OTHER },
];

const ticketStatusOptions = [
  { label: '全部状态', value: 0 },
  { label: '待处理', value: TicketApi.TICKET_STATUS.PENDING },
  { label: '处理中', value: TicketApi.TICKET_STATUS.PROCESSING },
  { label: '待回复', value: TicketApi.TICKET_STATUS.WAITING_REPLY },
  { label: '已解决', value: TicketApi.TICKET_STATUS.RESOLVED },
  { label: '已关闭', value: TicketApi.TICKET_STATUS.CLOSED },

]



// 表格列定义
const columns = [
  { name: 'no', align: 'left', label: '工单号', field: 'no', sortable: false },
  { name: 'type', align: 'left', label: '类型', field: 'type', sortable: false },
  { name: 'title', align: 'left', label: '标题', field: 'title', sortable: false },
  // { name: 'priority', align: 'left', label: '优先级', field: 'priority', sortable: true },
  { name: 'status', align: 'left', label: '状态', field: 'status', sortable: false },
  { name: 'createTime', align: 'left', label: '创建时间', field: 'createTime', sortable: false, format: (val) => formatDateTime(val) },
  { name: 'updateTime', align: 'left', label: '更新时间', field: 'updateTime', sortable: false, format: (val) => formatDateTime(val) },
  { name: 'actions', align: 'center', label: '操作' },
];

// 使用与notice/index.vue相同的分页数据结构
const state = reactive({
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 10,
  },
  loadStatus: false,
});

// Quasar表格分页设置
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  sortBy: 'createTime',
  descending: true,
});

// 新建工单弹窗
const newTicketDialog = ref(false);

// 关闭工单确认弹窗
const closeTicketDialog = ref(false);
const ticketToClose = ref(null);

// 加载工单列表
const loadTickets = async () => {
  state.loadStatus = true;
  try {
    const params = {
      pageNo: state.pagination.pageNo,
      pageSize: state.pagination.pageSize,
    };

    // 添加筛选条件
    if (filter.type > 0) {
      params.type = filter.type;
    }
    if (filter.status > 0) {
      params.status = filter.status;
    }

    const response = await TicketApi.getPage(params);
    if (response.code === 0) {
      state.pagination.list = response.data.list || [];
      state.pagination.total = response.data.total || 0;

      // 同时更新Quasar表格的分页信息
      pagination.value.page = state.pagination.pageNo;
    }
  } catch (error) {
    console.error('加载工单失败', error);
    $q.notify({
      color: 'negative',
      message: '加载工单失败',
      icon: 'error',
    });
  } finally {
    state.loadStatus = false;
  }
};

// 表格请求处理
const onRequest = (props) => {
  if (props.pagination) {
    pagination.value = props.pagination;
    // 同步到state
    state.pagination.pageNo = props.pagination.page;
    state.pagination.pageSize = props.pagination.rowsPerPage;
  }
  loadTickets();
};

// 处理页码变化（移动端分页）
const onPageChange = (page) => {
  state.pagination.pageNo = page;
  pagination.value.page = page;
  loadTickets();
};

// 打开工单详情
const openTicketDetail = (ticket) => {
  router.push({
    path: '/account/ticket-detail',
    query: { id: ticket.id }
  });
};

// 打开新建工单弹窗
const openNewTicketDialog = () => {
  newTicketDialog.value = true;
};

// 工单创建成功回调
const onTicketCreated = () => {
  loadTickets();
};

// 筛选变化时重新加载
const onFilterChange = () => {
  state.pagination.pageNo = 1;
  pagination.value.page = 1;
  loadTickets();
};

// 确认关闭工单
const confirmCloseTicket = (ticket) => {
  ticketToClose.value = ticket;
  closeTicketDialog.value = true;
};

// 关闭工单
const closeTicket = async () => {
  try {
    const response = await TicketApi.close(ticketToClose.value.id);
    if (response.code === 0) {
      $q.notify({
        color: 'positive',
        message: '工单关闭成功',
        icon: 'check',
      });
      loadTickets();
    }
  } catch (error) {
    console.error('关闭工单失败', error);
    $q.notify({
      color: 'negative',
      message: '关闭工单失败',
      icon: 'error',
    });
  }
};



// 获取工单类型颜色
const getTicketTypeColor = (type) => {
  return TicketApi.getTypeColor(type);
};

// 获取工单类型名称
const getTicketTypeName = (type) => {
  return TicketApi.getTypeName(type);
};

// 获取状态颜色
const getStatusColor = (status) => {
  return TicketApi.getStatusColor(status);
};

// 获取状态名称
const getStatusName = (status) => {
  return TicketApi.getStatusName(status);
};

// 获取优先级名称
const getPriorityName = (priority) => {
  return TicketApi.getPriorityName(priority);
};

// 格式化日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  const dateObj = new Date(timestamp);
  return date.formatDate(dateObj, 'YYYY-MM-DD HH:mm');
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const dateObj = new Date(timestamp);
  return date.formatDate(dateObj, 'YYYY-MM-DD');
};

// 页面加载时获取工单列表
onMounted(() => {
  loadTickets();
});
</script>

<style lang="scss" scoped>
.message-consult {
  .filter-section {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  }

  .ticket-filter {
    min-width: 150px;
  }

  .ticket-list {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  }

  // 移动端样式
  @media (max-width: 599px) {
    .filter-section {
      .q-select {
        margin-bottom: 8px;
      }
    }

    .mobile-view {
      .q-item {
        padding: 12px;
      }
    }
  }
}
</style>
